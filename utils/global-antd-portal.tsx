import { StyleProvider } from '@ant-design/cssinjs';
import { ConfigProvider } from 'antd';
import ReactDOM from 'react-dom/client';

let globalConfigProviderRoot: ReactDOM.Root | null = null; // 全局 ConfigProvider 的 root
let initPromise: Promise<void> | null = null;

/**
 * 初始化全局 ConfigProvider 到宿主页面
 * 这个全局 ConfigProvider 会处理所有 antd 组件的配置和样式
 * 包括 Shadow DOM 内的组件和弹出到宿主页面的组件
 */
const initGlobalConfigProvider = () => {
  if (globalConfigProviderRoot) {
    return; // 已经初始化过了
  }

  try {
    // 1. 创建全局 ConfigProvider 容器
    const globalConfigContainer = document.createElement('div');
    globalConfigContainer.id = 'inhand-global-antd-config-provider';
    globalConfigContainer.style.display = 'none';

    document.body.appendChild(globalConfigContainer);

    // 2. 渲染全局 StyleProvider 和 ConfigProvider
    // 这个全局的 StyleProvider 会处理所有 antd 组件的样式注入
    globalConfigProviderRoot = ReactDOM.createRoot(globalConfigContainer);
    globalConfigProviderRoot.render(
      <StyleProvider container={document.head}>
        <ConfigProvider
          theme={{
            token: {
              colorPrimary: '#1677ff',
              borderRadius: 6,
              zIndexPopupBase: **********,
            },
          }}
          // 关键配置：将所有弹出组件渲染到宿主页面的 body
          getPopupContainer={() => document.body}
        >
          {/* 空的 div，只是为了让 ConfigProvider 在全局生效 */}
          <div />
        </ConfigProvider>
      </StyleProvider>,
    );

    console.log('[InHand] Global ConfigProvider and StyleProvider initialized');
    console.log('[InHand] All antd components will now use global configuration');
  } catch (error) {
    console.error('[InHand] Failed to initialize global ConfigProvider:', error);
  }
};

const createPortal = () => {
  if (window.self !== window.top) {
    return;
  }

  // 检查是否已经初始化
  if (document.getElementById('inhand-global-antd-config-provider')) {
    return; // 已经初始化过了
  }

  // 初始化全局 ConfigProvider 到宿主页面
  initGlobalConfigProvider();
};

export const initAntdPortal = (): Promise<void> => {
  if (window.self !== window.top) {
    return Promise.resolve();
  }
  if (!initPromise) {
    initPromise = new Promise((resolve) => {
      if (document.readyState === 'complete' || document.readyState === 'interactive') {
        createPortal();
        resolve();
      } else {
        window.addEventListener('DOMContentLoaded', () => {
          createPortal();
          resolve();
        });
      }
    });
  }
  return initPromise;
};

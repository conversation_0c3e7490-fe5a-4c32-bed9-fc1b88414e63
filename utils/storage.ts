import { browser } from '#imports';

export const getCookiesForCurrentDomain = async () => {
  return await browser.runtime.sendMessage({ action: 'getCookiesForCurrentTab' });
};

export const getRequestCookies = async () => {
  const requiredCookies = ['_iamadt', '_iambdt', 'crmcsr'];
  const allCookies = await getCookiesForCurrentDomain();
  return allCookies?.filter((cookie: any) => requiredCookies.includes(cookie.name));
};

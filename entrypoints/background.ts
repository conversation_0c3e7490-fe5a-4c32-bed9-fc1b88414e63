import { defineBackground } from '#imports';
// @ts-ignore
import psl from 'psl';

const getCookiesForCurrentTab = async (message: any, sender: any, sendResponse: any) => {
  try {
    // 获取当前活动标签页的信息，以便获取其 URL
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    if (tab && tab.url) {
      // 解析 URL 以获取域名信息
      const url = new URL(tab.url);
      const hostname = url.hostname;
      // 获取指定域名的所有 cookie（包括 HttpOnly）
      const cookies = await chrome.cookies.getAll({
        domain: hostname,
      });

      // 同时获取父域名的 cookie（例如 .example.com）
      const domainParts = psl.get(hostname);
      const parentDomainCookies = [];

      if (domainParts) {
        const parentDomain = '.' + domainParts;
        const parentCookies = await chrome.cookies.getAll({
          domain: parentDomain,
        });
        parentDomainCookies.push(...parentCookies);
      }

      // 合并所有 cookie 并去重
      const allCookies = [...cookies, ...parentDomainCookies];
      const uniqueCookies = allCookies.filter(
        (cookie, index, self) =>
          index === self.findIndex((c) => c.name === cookie.name && c.domain === cookie.domain),
      );

      // 将结果发送回请求者 (popup 或 content script)
      sendResponse({
        success: true,
        cookies: uniqueCookies,
        hostname: hostname,
        url: tab.url,
      });
      return uniqueCookies;
    } else {
      sendResponse({ success: false, message: '[inhand] Could not get current tab URL.' });
      return false;
    }
  } catch (error) {
    console.error('[inhand] Error getting cookies:', error);
    sendResponse({
      success: false,
      message: `[inhand] Error getting cookies: ${error instanceof Error ? error.message : String(error)}`,
    });
    return false;
  }
};

export default defineBackground(async () => {
  console.log('Sales Agent Plugin initialing');
  browser.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
    if (message.action === 'getCookiesForCurrentTab') {
      return getCookiesForCurrentTab(message, sender, sendResponse);
    }
  });
});

export default defineUnlistedScript(() => {
  // 在主世界中运行的代码
  function getUserId() {
    const crmZuid = (window as any).crmZuid; // 替换为实际变量名
    if (crmZuid) {
      return crmZuid;
    }
    const zoho = (window as any).$zoho; // 替换为实际变量名
    return zoho?.salesiq?.values?.info?.zuid;
  }

  function getCurrentCsrfToken() {
    return (window as any).csrfToken;
  }

  const csrfToken = getCurrentCsrfToken();

  const zohoUserID = getUserId();
  window.postMessage(
    {
      type: 'inhand:zohoUserID',
      data: zohoUserID,
    },
    '*',
  );
  window.postMessage(
    {
      type: 'inhand:csrfToken',
      data: csrfToken,
    },
    '*',
  );

  // 监听实时获取 csrfToken 的请求
  window.addEventListener('message', (event) => {
    if (event.source !== window) return;

    if (event.data.type === 'inhand:getCsrfToken') {
      window.postMessage(
        {
          type: 'inhand:csrfTokenResponse',
          data: getCurrentCsrfToken(),
        },
        '*',
      );
    }
  });
});

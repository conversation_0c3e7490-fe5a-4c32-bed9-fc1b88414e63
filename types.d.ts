/// <reference path="./.wxt/types/globals.d.ts" />
/// <reference path="./.wxt/types/i18n.d.ts" />
/// <reference path="./.wxt/types/imports.d.ts" />
/// <reference path="./.wxt/types/paths.d.ts" />

/// <reference types="vite-plugin-svgr/client" />

// 确保这些类型在全局范围内可用
declare global {
  // 这里可以添加任何需要在全局范围内可用的类型
  // 例如：
  // type GlobalType = import('./.wxt/types/globals').SomeType;

  // 为 window 对象添加自定义属性
  interface Window {
    inhandBatchDetectCheckedIds: Record<string, boolean>;
    inhandBatchDetectMountUI: any;
    inhandBatchDetectUi?: any;
  }
}

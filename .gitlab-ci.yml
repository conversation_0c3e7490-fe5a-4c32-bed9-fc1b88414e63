stages:
  - deps
  - build
  - zip

default:
  tags:
    - docker

fetch-deps:
  variables:
    CACHE_FALLBACK_KEY: master
  image: registry.inhand.design/docker/node:22.14-pnpm.10
  allow_failure: true
  stage: deps
  cache:
    - key: '$CI_COMMIT_REF_SLUG'
      paths:
        - node_modules
  rules:
    - when: manual
    - changes:
        - package.json
  before_script: # 将 corepack enable 移到 before_script
    - corepack enable
  script:
    - pnpm install

build:
  stage: build
  image: registry.inhand.design/docker/node:22.14-pnpm.10
  script:
    - if [ ! -d node_modules ]; then pnpm install; fi;
    - pnpm build
  cache:
    - policy: pull-push
      key:
        prefix: $CI_JOB_NAME
        files:
          - package.json
      paths:
        - .wxt
  artifacts:
    paths:
      - dist
  only:
    - main
    - tags

lint:
  stage: build
  image: registry.inhand.design/docker/node:22.14-pnpm.10
  tags:
    - docker
  allow_failure: true
  before_script: # 将 corepack enable 移到 before_script
    - corepack enable
  script:
    - if [ ! -d node_modules ]; then pnpm install; fi;
    - pnpm lint
  cache:
    key:
      files:
        - package.json
    policy: pull
    paths:
      - node_modules
  rules:
    - when: always

# Developer Guide / 开发者指南

## Project Overview / 项目概述

Inland Assistant is a browser extension built with modern web technologies. The project uses React, TypeScript, and TailwindCSS for development.

Inland Assistant 是一个使用现代 Web 技术构建的浏览器扩展。项目使用 React、TypeScript 和 TailwindCSS 进行开发。

## Tech Stack / 技术栈

- **Framework**: React 19
- **Language**: TypeScript
- **Styling**: TailwindCSS
- **Build Tool**: WXT (Web Extension Tools)
- **Package Manager**: pnpm
- **Code Quality**: ESLint, Prettier, <PERSON>sky

## Prerequisites / 前置要求

- Node.js (Latest LTS version recommended)
- pnpm 10.6.5
- Git

## Getting Started / 开始使用

1. Clone the repository

```bash
git clone [repository-url]
cd inhand-assistant
```

2. Install dependencies

```bash
pnpm install
```

3. Start development server

```bash
pnpm dev
```

## Available Scripts / 可用脚本

- `pnpm dev` - Start development server for Chrome
- `pnpm dev:firefox` - Start development server for Firefox
- `pnpm build` - Build extension for Chrome
- `pnpm build:firefox` - Build extension for Firefox
- `pnpm zip` - Create zip package for Chrome
- `pnpm zip:firefox` - Create zip package for Firefox
- `pnpm compile` - Type check TypeScript files
- `pnpm lint` - Run ESLint
- `pnpm lint:fix` - Fix ESLint issues
- `pnpm format` - Format code with Prettier

## Project Structure / 项目结构

```
inhand-assistant/
├── src/                # Source code
├── public/            # Static assets
├── manifest.json      # Extension manifest
└── package.json       # Project dependencies
```

## Development Guidelines / 开发指南

### Code Style / 代码风格

- Follow TypeScript best practices
- Use functional components with hooks
- Implement proper error handling
- Write meaningful comments
- Follow ESLint and Prettier configurations

### Git Workflow / Git 工作流

1. Create a new branch for your feature
2. Make your changes
3. Run linting and formatting
4. Submit a pull request

### Browser Support / 浏览器支持

- Chrome
- Firefox

## Testing / 测试

Before submitting code:

- Run `pnpm compile` to check for TypeScript errors
- Run `pnpm lint` to ensure code quality
- Run `pnpm format` to format code

## Building for Production / 生产环境构建

1. Build the extension:

```bash
pnpm build        # For Chrome
pnpm build:firefox # For Firefox
```

2. Create distribution package:

```bash
pnpm zip          # For Chrome
pnpm zip:firefox  # For Firefox
```

## Troubleshooting / 故障排除

Common issues and solutions:

1. If dependencies are not installing correctly:

   ```bash
   pnpm install --force
   ```

2. If TypeScript errors persist:
   ```bash
   pnpm compile
   ```

## Contributing / 贡献指南

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License / 许可证

[Add license information]

## Support / 支持

For support, please open an issue in the repository.

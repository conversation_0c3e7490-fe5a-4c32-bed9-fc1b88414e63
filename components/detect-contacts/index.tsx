import React, { useState } from 'react';

import DetectContactPopover from '@/components/detect-contact-popover';
import { getAccountId, getAccountName } from '@/utils';
import { useRequest } from 'ahooks';
import type { TaskStatusType } from '../detect-contact-popover/data';
import DetectDropdown from './detect-dropdown';
import { fetchTaskResultByAccountId } from './service';

import { DownOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import MagicSVG from '~/assets/magic-colorful.svg';

export const DetectContacts: React.FC = () => {
  const accountId = getAccountId() as string;
  const accountName = getAccountName() as string;

  const [taskId, setTaskId] = useState<string | undefined>(undefined);
  const [taskStatus, setTaskStatus] = useState<TaskStatusType>();

  useRequest(
    async () => {
      if (!accountId) return;
      const { _id: taskId, status } = await fetchTaskResultByAccountId(accountId);
      setTaskId(taskId);
      setTaskStatus(status);
    },
    {
      refreshDeps: [accountId],
    },
  );

  const taskRunning = ['running', 'pending'].includes(taskStatus || '');

  return (
    <DetectDropdown
      accountId={accountId}
      onCreateTask={(newTaskId) => {
        setTaskId(newTaskId);
      }}
      disabled={taskRunning}
    >
      <DetectContactPopover
        placement='left'
        taskId={taskId}
        accountName={accountName}
        accountId={accountId}
        onTaskStatusUpdate={(status) => {
          setTaskStatus(status);
        }}
      >
        <Button
          type='default'
          size='small'
          icon={<img src={MagicSVG} alt='magic' width={20} style={{ color: '#fff' }} />}
          loading={taskRunning}
          disabled={taskRunning}
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '8px',
            verticalAlign: 'middle',
            height: '28px',
            lineHeight: '28px',
          }}
        >
          Detect Contacts
          <DownOutlined />
        </Button>
      </DetectContactPopover>
    </DetectDropdown>
  );
};

export default DetectContacts;

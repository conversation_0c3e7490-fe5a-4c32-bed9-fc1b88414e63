import type { CustomPromptsType } from '@/components/custom-prompt-modal';

import { getCurrentUser } from '@/utils';
import request from '@/utils/request';
import { getZohoHeaderRequest } from '@/utils/zoho-request-header';

export interface CompanyInfoType {
  accountId: string;
  ownerId: string;
  customPrompts?: CustomPromptsType;
}

export const fetchTaskResultByAccountId = async (accountId: string) => {
  return await request(`/api/sales-agent/account/${accountId}/task`, {
    method: 'GET',
  });
};

export const fetchTaskResultByAccountIds = async (accountIds: string[]) => {
  return await request('/api/sales-agent/accounts/tasks', {
    method: 'POST',
    data: {
      account_ids: accountIds.filter(Boolean),
    },
  });
};

export const startDetectTask = async (companyInfo: CompanyInfoType) => {
  return await request('/api/sales-agent/detect-contacts/start', {
    method: 'POST',
    data: {
      account_id: companyInfo.accountId,
      owner_id: companyInfo.ownerId,
      custom_prompts: companyInfo.customPrompts,
      current_user: getCurrentUser(),
      request_headers: await getZohoHeaderRequest(),
    },
  });
};

import ReactDOM from 'react-dom/client';
import DetectContacts from './index.tsx';

import type { ContentScriptContext } from '#imports';
import { createAutoMountShadowRootUI } from '@/utils';

// Maintain a global state to track if the UI is mounted
export const renderDetectContacts = async (ctx: ContentScriptContext) => {
  await createAutoMountShadowRootUI<ReactDOM.Root | undefined>(ctx, {
    name: 'inhand-detect-button-ui',
    position: 'inline',
    pathMatches: ['/crm/*/tab/Accounts/*'],
    anchor: 'crm-related-list-view-header[related-list-label="Contacts"] crm-related-list-actions',
    append: 'first',
    inheritStyles: true,
    isolateEvents: false,
    onMount: (container: HTMLElement, shadow: ShadowRoot, shadowHost: HTMLElement) => {
      if (shadowHost) {
        shadowHost.style.display = 'inline-block';
        shadowHost.style.position = 'relative';
        shadowHost.style.verticalAlign = 'middle';
        shadowHost.style.margin = '4px 8px 4px 0';
        shadowHost.style.textAlign = 'left';
        shadowHost.classList.add('inhand-detect-button-host');
      }

      if (container) {
        container.style.display = 'inline-block';
        container.style.position = 'relative';
        container.style.verticalAlign = 'middle';

        const reactContainer = document.createElement('div');
        reactContainer.id = 'detect-contacts-container';
        reactContainer.style.display = 'inline-block';
        reactContainer.style.verticalAlign = 'middle';

        // 确保 shadow DOM 有 head 元素
        let shadowHead = shadow.querySelector('head');
        if (!shadowHead) {
          shadowHead = document.createElement('head');
          shadow.appendChild(shadowHead);
        }

        container.appendChild(reactContainer);
        let currentRoot = ReactDOM.createRoot(reactContainer);

        currentRoot.render(<DetectContacts />);
        return currentRoot;
      }

      return undefined;
    },
    onRemove: (root: ReactDOM.Root | undefined) => {
      root?.unmount();
    },
  });
};

import React from 'react';

import { useRequest } from 'ahooks';
import { Dropdown, type DropdownProps, message } from 'antd';

import type { CustomPromptsType } from '../../custom-prompt-modal';
import CustomPromptModalModal from '../../custom-prompt-modal';
import { startDetectTask } from '../service';

import { getUserId } from '@/utils';

interface DetectDropdownTypes extends DropdownProps {
  onCreateTask?: (taskId: string) => void;
  taskRunning?: boolean;
  accountId?: string;
  children?: React.ReactNode;
}

export const DetectDropdown: React.FC<DetectDropdownTypes> = ({
  onCreateTask,
  taskRunning,
  accountId,
  children,
  ...restProps
}) => {
  const [messageApi, contextHolder] = message.useMessage();

  const showErrorMessage = useCallback(
    (error: string) => {
      messageApi.error(error);
    },
    [messageApi],
  );

  // ===== 发起探查任务 =====
  const { run: handleDetectContacts, loading: creating } = useRequest(
    async (customPrompts?: CustomPromptsType) => {
      const ownerId = (await getUserId()) as string;

      const { task_id: newTaskId } = await startDetectTask({
        accountId: accountId as string,
        ownerId,
        customPrompts,
      });

      if (newTaskId) {
        onCreateTask?.(newTaskId);
      } else {
        showErrorMessage('Failed to get task_id from startDetectTask');
      }
    },
    {
      manual: true,
      onError: async (error) => {
        showErrorMessage(`Failed to get task_id from startDetectTask: ${JSON.stringify(error)}`);
      },
    },
  );

  const loading = taskRunning || creating;

  return (
    <>
      <Dropdown
        {...restProps}
        trigger={['click']}
        menu={{
          items: [
            {
              key: 'standard',
              onClick: () => handleDetectContacts(),
              label: 'Use Standard Detect',
              disabled: loading,
            },
            {
              key: 'custom',
              disabled: loading,
              label: (
                <CustomPromptModalModal onConfirm={(prompt) => handleDetectContacts(prompt)} />
              ),
            },
          ],
        }}
        overlayStyle={{ width: 180 }}
      >
        {children}
      </Dropdown>
      {contextHolder}
    </>
  );
};

export default DetectDropdown;

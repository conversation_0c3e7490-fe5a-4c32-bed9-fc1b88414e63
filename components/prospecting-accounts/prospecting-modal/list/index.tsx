import { getAccountId } from '@/utils';

import { PlusOutlined } from '@ant-design/icons';
import { Conversations } from '@ant-design/x';
import { useControllableValue, useRequest } from 'ahooks';
import { <PERSON><PERSON>, Flex, Spin } from 'antd';
import { forwardRef, useImperativeHandle } from 'react';

import type { HistoryItem } from '../../data';
import { getProspectingThreadByAccountId } from '../../service.ts';

export interface ListRef {
  fetchHistory: () => void;
}

interface ListProps {
  onDataChange?: (data: any[]) => void;
  activeKey?: string;
  onSelectChange?: (key: string | undefined) => void;
}

const List = forwardRef<ListRef, ListProps>(({ onDataChange, activeKey, onSelectChange }, ref) => {
  const accountId = getAccountId();
  const [activeListKey, setActiveListKey] = useControllableValue<string | undefined>({
    value: activeKey,
    onChange: onSelectChange,
  });

  const {
    run: fetchHistory,
    data,
    loading,
  } = useRequest(
    async (threadId?: string) => {
      if (!accountId) {
        return [];
      }
      const res = await getProspectingThreadByAccountId(accountId);
      const newActiveKey = threadId ?? res[0]?._id;
      setActiveListKey(newActiveKey);
      const mappedData = (res ?? []).map((v: HistoryItem) => ({ key: v._id, label: v.title }));
      onDataChange?.(mappedData);
      return mappedData;
    },
    {
      refreshDeps: [accountId],
    },
  );

  useImperativeHandle(ref, () => ({
    fetchHistory,
  }));

  if (data?.length === 0) {
    return null;
  }

  return (
    <Spin spinning={loading}>
      <Conversations
        style={{
          width: '100%',
          padding: 0,
          height: 558,
          overflow: 'auto',
        }}
        activeKey={activeListKey}
        items={data}
        onActiveChange={(v) => {
          setActiveListKey(v);
        }}
      />
      <Flex align='center' justify='center'>
        <Button
          icon={<PlusOutlined />}
          style={{
            width: '100%',
            marginTop: 12,
          }}
          onClick={() => {
            setActiveListKey(undefined);
          }}
        >
          New
        </Button>
      </Flex>
    </Spin>
  );
});

List.displayName = 'List';

export default List;

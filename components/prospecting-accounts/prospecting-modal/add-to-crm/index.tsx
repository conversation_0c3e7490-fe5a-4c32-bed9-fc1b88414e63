import React from 'react';

import { CheckCircleOutlined, CloseCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Tooltip } from 'antd';

import type { AddAccountInfoType } from '../../service';
import { addAccountToCRM } from '../../service';

import type { ProspectingAccountInfo } from '../../data';

import { getUserId } from '@/utils';
import { getZohoHeaderRequest } from '@/utils/zoho-request-header';

interface AddToCrmProps {
  info: ProspectingAccountInfo;
}

const AddToCrm: React.FC<AddToCrmProps> = ({ info }) => {
  const {
    loading,
    run: handleAdd,
    data,
  } = useRequest(
    async () => {
      const user_id = (await getUserId()) as string;
      const data: AddAccountInfoType = {
        user_id,
        account_info: info.account_info,
        request_headers: await getZohoHeaderRequest(),
      };
      return await addAccountToCRM(data);
    },
    {
      manual: true,
      refreshDeps: [info],
    },
  );

  return (
    <>
      <Button
        type='text'
        loading={loading}
        icon={<PlusOutlined />}
        size='small'
        onClick={() => handleAdd()}
      >
        Add to CRM
      </Button>
      {data &&
        (data?.account_id ? (
          <Tooltip
            placement='left'
            title={'Added to CRM successfully'}
            getPopupContainer={(node) => node.parentElement!}
          >
            <CheckCircleOutlined
              style={{
                color: '#52c41a',
                cursor: 'pointer',
                marginLeft: 8,
              }}
            />
          </Tooltip>
        ) : (
          <Tooltip
            placement='left'
            title={data?.detail || 'Added to CRM failed'}
            getPopupContainer={(node) => node.parentElement!}
          >
            <CloseCircleOutlined
              style={{
                color: '#ff4d4f',
                cursor: 'pointer',
                marginLeft: 8,
              }}
            />
          </Tooltip>
        ))}
    </>
  );
};

export default AddToCrm;

import React, { useCallback, useMemo, useRef, useState } from 'react';

import { ArrowDownOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { Sender } from '@ant-design/x';
import { useControllableValue, useMount, useRequest } from 'ahooks';
import {
  Alert,
  Button,
  Card,
  Descriptions,
  Flex,
  FloatButton,
  List,
  Space,
  Spin,
  Typography,
} from 'antd';

import type { EventStreamItem, ProspectingAccountInfo, TaskStatusType } from '../../data';
import {
  getProspectingThreadProfileById,
  prospectingAccountsByAccountId,
  prospectingMoreAccountsByThreadId,
} from '../../service';
import AddToCrm from '../add-to-crm';

import { getAccountId } from '@/utils';
import { BASE_URL, X_API_KEY } from '@/utils/request.ts';
import type { EventSourceMessage } from '@microsoft/fetch-event-source';
import { fetchEventSource } from '@microsoft/fetch-event-source';

import MarkdownTypingPreview from '@/components/markdown-typing-preview';
import MarkdownView from '@/components/markdown-view';
import customCss from './index.css?raw';

let targetDocument: Document;
try {
  targetDocument = window.top!.document;
} catch {
  targetDocument = window.document;
}
const getStyleContainer = (): HTMLElement => targetDocument.head;

interface DialogueProps {
  refreshList?: () => void;
  threadId?: string;
  onChangeThreadId?: (taskId?: string | undefined) => void;
}

const Dialogue: React.FC<DialogueProps> = ({ refreshList, threadId, onChangeThreadId }) => {
  const accountId = getAccountId();
  const profileListRef = useRef<HTMLDivElement>(null);
  const [showScrollArrow, setShowScrollArrow] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [prospectingData, setProspectingData] = useState<ProspectingAccountInfo[]>([]);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);

  const [thinkingEvent, setThinkingEvent] = useState<EventStreamItem | null>(null);
  const ctrlRef = useRef<AbortController | null>(null);

  const [activeThreadId, setActiveThreadId] = useControllableValue<string | undefined>({
    value: threadId,
    onChange: onChangeThreadId,
  });

  const [listLoading, setListLoading] = useState(false);
  const [prospecting, setProspecting] = useState<boolean>(false);
  const [prospectingMoreLoading, setProspectingMoreLoading] = useState<boolean>(false);

  useMount(() => {
    const styleElement = targetDocument.createElement('style');
    styleElement.id = 'prospecting-modal-dialogue-custom-styles';
    styleElement.innerHTML = customCss;

    if (!targetDocument.getElementById(styleElement.id)) {
      getStyleContainer().appendChild(styleElement);
    }

    return () => {
      const existingStyle = targetDocument.getElementById(styleElement.id);
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  });

  const handleProfileListScroll = useCallback(() => {
    const el = profileListRef.current;
    if (!el) {
      return;
    }
    const { scrollTop, scrollHeight, clientHeight } = el;
    setShowScrollArrow(scrollTop + clientHeight < scrollHeight - 1);
  }, []);

  const scrollToTop = useCallback(() => {
    const el = profileListRef.current;
    if (!el) return;
    el.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  const scrollToBottom = useCallback(() => {
    const el = profileListRef.current;
    if (!el) return;
    el.scrollTo({
      top: el.scrollHeight,
      behavior: 'smooth',
    });
  }, []);

  useEffect(() => {
    const el = profileListRef.current;
    if (!el) return;
    handleProfileListScroll();
    el.addEventListener('scroll', handleProfileListScroll);
    return () => {
      el.removeEventListener('scroll', handleProfileListScroll);
    };
  }, [handleProfileListScroll, prospectingData]);

  const { run: handleReconnectSSE } = useRequest(
    async ({ runningTaskIds, threadId }) => {
      setProspectingMoreLoading(true);
      for (const runningTaskId of runningTaskIds) {
        await startEventSource({
          task_id: runningTaskId,
          thread_id: threadId,
          task_status: 'pending',
        });
      }
      setProspectingMoreLoading(false);
    },
    {
      manual: true,
    },
  );

  const {
    loading: historyThreadLoading,
    run: fetchThreadProfile,
    cancel: cancelFetchProfile,
    data: threadProfileData,
  } = useRequest(
    async (threadId) => {
      if (threadId) {
        setListLoading(true);
        const { data, running_task_ids, ...other } =
          await getProspectingThreadProfileById(threadId);
        setProspectingData(data);
        if (data?.length === 0) {
          setProspecting(true);
          setPrompt(other?.user_query ?? '');
        }
        if (running_task_ids?.length > 0) {
          handleReconnectSSE({
            runningTaskIds: running_task_ids,
            threadId,
          });
        }
        if (other?.error) {
          setErrorMessage(other.error);
        }
        setListLoading(false);
        return {
          ...other,
          data,
        };
      } else {
        setProspectingData([]);
        return undefined;
      }
    },
    {
      manual: true,
      onError: (e) => {
        setErrorMessage(JSON.stringify(e));
        setListLoading(false);
      },
    },
  );

  const startEventSource = useCallback(
    async ({
      task_id,
      thread_id,
      task_status,
      isMoreProspecting = false,
    }: {
      task_id: string;
      thread_id: string;
      task_status?: TaskStatusType;
      isMoreProspecting?: boolean;
    }) => {
      if (ctrlRef.current) {
        ctrlRef.current.abort();
      }

      const ctrl = new AbortController();
      ctrlRef.current = ctrl;
      setThinkingEvent({
        event: 'pending',
        status: task_status ?? 'pending',
        content: 'Ready to start',
      });

      // 根据是否是更多勘探来设置不同的loading状态
      if (isMoreProspecting) {
        setProspectingMoreLoading(true);
      } else {
        setProspecting(true);
      }

      await fetchEventSource(`${BASE_URL}/api/sales-agent/tasks/${task_id}/stream`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': X_API_KEY,
        },
        signal: ctrl.signal,
        onopen: async () => {
          setThinkingEvent({
            event: 'thinking',
            status: 'thinking',
            content: 'The current task is in the queue.',
          });
        },
        onmessage: async (event: EventSourceMessage) => {
          const parsedData: EventStreamItem = JSON.parse(event.data);

          if (
            parsedData?.type &&
            ['thinking', 'completed', 'finish'].includes(parsedData?.type as string)
          ) {
            if (parsedData?.type === 'thinking' || parsedData?.type === 'status') {
              setThinkingEvent(parsedData);
            }
            if (parsedData?.type === 'completed' || parsedData?.type === 'finish') {
              fetchThreadProfile(thread_id);
              setThinkingEvent(parsedData);
              setActiveThreadId(thread_id);
              refreshList?.();
            }
          } else if (parsedData.type === 'error') {
            setThinkingEvent({
              event: 'error',
              status: 'failed',
              content: parsedData.content || 'An error occurred',
            });
            refreshList?.();
            setErrorMessage(parsedData.content || 'An error occurred');
          }
        },
        onclose: async () => {
          // 根据是否是更多勘探来清除不同的loading状态
          if (isMoreProspecting) {
            setProspectingMoreLoading(false);
          } else {
            setProspecting(false);
          }
          ctrl.abort();
          ctrlRef.current = null;
        },
        onerror: () => {
          // 根据是否是更多勘探来清除不同的loading状态
          if (isMoreProspecting) {
            setProspectingMoreLoading(false);
          } else {
            setProspecting(false);
          }
          ctrl.abort();
          ctrlRef.current = null;
        },
      });
    },
    [fetchThreadProfile, setActiveThreadId, refreshList],
  );

  const { run: handleProspecting, cancel: cancelProspecting } = useRequest(
    async ({ prompt }: { prompt: string }) => {
      setProspecting(true);
      const { task_id, error, thread_id, task_status } = await prospectingAccountsByAccountId({
        account_id: accountId,
        user_query: prompt,
      });
      if (thread_id) {
        setActiveThreadId(thread_id);
        refreshList?.();
      }
      if (task_id) {
        await startEventSource({
          task_id,
          thread_id,
          task_status,
          isMoreProspecting: false,
        });
      }
      if (error) {
        setErrorMessage(error);
      }
      setProspecting(false);
    },
    {
      manual: true,
      refreshDeps: [accountId, prompt],
    },
  );

  const { run: handleProspectingMore } = useRequest(
    async () => {
      if (!activeThreadId) {
        return;
      }
      setProspectingMoreLoading(true);
      const { task_id, error, thread_id, task_status } =
        await prospectingMoreAccountsByThreadId(activeThreadId);

      scrollToBottom();

      if (task_id) {
        await startEventSource({
          task_id,
          thread_id,
          task_status,
          isMoreProspecting: true,
        });
      }
      if (error) {
        setErrorMessage(error);
      }
      setProspectingMoreLoading(false);
    },
    {
      manual: true,
      refreshDeps: [activeThreadId],
    },
  );

  useEffect(() => {
    setProspecting(false);
    cancelFetchProfile();
    cancelProspecting?.();
    scrollToTop();
    setPrompt('');
    setThinkingEvent(null);
    ctrlRef?.current?.abort();
    setErrorMessage(undefined);
    fetchThreadProfile(activeThreadId);
  }, [activeThreadId, fetchThreadProfile, cancelFetchProfile, cancelProspecting, scrollToTop]);

  const dialogLoading = useMemo(
    () => prospecting || historyThreadLoading || prospectingMoreLoading,
    [prospecting, historyThreadLoading, prospectingMoreLoading],
  );

  const handleRetry = useCallback(() => {
    setErrorMessage(undefined);
    setProspectingMoreLoading(true);
    handleProspectingMore();
  }, [handleProspectingMore]);

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        position: 'relative',
      }}
      className='prospecting-dialogue'
    >
      {prospectingData && prospectingData?.length > 0 ? (
        <div style={{ position: 'relative', height: 602 }}>
          <List
            className='list-container'
            header={
              <Descriptions
                column={1}
                items={[
                  {
                    label: 'Prospecting Condition',
                    children: threadProfileData?.user_query,
                  },
                ]}
              />
            }
            // @ts-ignore
            ref={profileListRef as HTMLDivElement}
            loading={listLoading}
            style={{ height: '100%', overflow: 'auto' }}
            dataSource={prospectingData}
            renderItem={(item: ProspectingAccountInfo, index: number) => {
              return (
                <List.Item
                  style={{
                    padding: 0,
                    borderRadius: 8,
                    marginBottom: 16,
                    boxShadow:
                      '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
                  }}
                >
                  <Card
                    size='small'
                    title={
                      <div
                        style={{
                          fontWeight: 600,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 8,
                        }}
                      >
                        <span
                          style={{
                            fontSize: 18,
                            color: '#999',
                            flexShrink: 0,
                            lineHeight: '1.4',
                          }}
                        >
                          {index + 1}.
                        </span>
                        <Typography.Text
                          ellipsis
                          style={{ width: 500, fontSize: 18, lineHeight: '1.4' }}
                        >
                          {item.account_info.name}
                        </Typography.Text>
                      </div>
                    }
                    extra={<AddToCrm info={item} key={item.account_info.website} />}
                    style={{
                      width: '100%',
                    }}
                  >
                    <MarkdownView content={item.content} className='dialogue-markdown-view' />
                  </Card>
                </List.Item>
              );
            }}
            loadMore={
              prospectingMoreLoading ? (
                <Flex
                  vertical
                  style={{
                    height: 460,
                    width: '100%',
                  }}
                  justify={'center'}
                  align={'center'}
                >
                  <Spin spinning size='large' />
                  <Typography.Text
                    type='secondary'
                    style={{
                      maxHeight: 400,
                      padding: '12px 16px',
                    }}
                  >
                    <MarkdownTypingPreview
                      markdownText={
                        typeof thinkingEvent?.content === 'string'
                          ? thinkingEvent.content
                          : 'Ready to start'
                      }
                    />
                  </Typography.Text>
                </Flex>
              ) : (
                <Space
                  direction='vertical'
                  style={{
                    width: '100%',
                  }}
                >
                  {errorMessage && (
                    <Alert
                      type='error'
                      message='An error occurred while fetching data. Please check the error message below.'
                      description={errorMessage}
                      showIcon={true}
                      banner={true}
                      style={{
                        borderRadius: 8,
                        margin: '0 12px',
                      }}
                    />
                  )}
                  <Flex justify='center' align='center'>
                    <Button
                      icon={
                        <DoubleRightOutlined
                          style={{
                            transform: 'rotate(90deg)',
                          }}
                        />
                      }
                      onClick={() => {
                        handleProspectingMore();
                        setErrorMessage(undefined);
                      }}
                    >
                      Prospecting More
                    </Button>
                  </Flex>
                </Space>
              )
            }
          />
          {showScrollArrow && (
            <FloatButton
              icon={<ArrowDownOutlined />}
              shape='circle'
              style={{
                position: 'absolute',
                left: 'calc(50% - 20px)',
                bottom: 20,
              }}
              onClick={scrollToBottom}
            />
          )}
        </div>
      ) : (
        <div
          style={{
            height: '100%',
            alignItems: 'center',
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignContent: 'center',
          }}
        >
          {dialogLoading && (
            <Flex
              vertical
              style={{
                height: '100%',
                width: '100%',
              }}
              justify={'center'}
              align={'center'}
            >
              <Spin spinning size='large' />
              <Typography.Text
                type='secondary'
                style={{
                  maxHeight: 'calc(100% - 62px)',
                  padding: '12px 16px',
                }}
              >
                <MarkdownTypingPreview markdownText={thinkingEvent?.content || 'Ready to start'} />
              </Typography.Text>
            </Flex>
          )}
          {errorMessage && (
            <Typography>
              <Typography.Title level={5} type='danger'>
                An error occurred while fetching data. Please try again.{' '}
                <Button
                  onClick={handleRetry}
                  type='link'
                  style={{
                    marginLeft: 8,
                  }}
                >
                  Retry
                </Button>
              </Typography.Title>
              <Typography.Paragraph
                copyable
                style={{
                  fontSize: 12,
                  color: 'rgba(0,0,0,0.6)',
                }}
              >
                {errorMessage}
              </Typography.Paragraph>
            </Typography>
          )}
          {!threadId && (
            <Sender
              disabled={dialogLoading}
              loading={dialogLoading}
              value={prompt}
              onChange={setPrompt}
              onSubmit={() => {
                handleProspecting({ prompt });
                setErrorMessage(undefined);
              }}
              placeholder='I want to know companies like ingersoll rand who use edge gateways for its air compressors remote monitoring'
              autoSize={{
                minRows: 8,
                maxRows: 8,
              }}
            />
          )}
        </div>
      )}
    </div>
  );
};

Dialogue.displayName = 'Dialogue';

export default Dialogue;

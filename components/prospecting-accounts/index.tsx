import React, { useState } from 'react';

import { Button } from 'antd';

import MagicSVG from '~/assets/magic-colorful.svg';
import ProspectingModal from './prospecting-modal';

const ProspectingAccounts: React.FC = () => {
  const [prospectingModalOpen, setProsPendingModalOpen] = useState(false);

  return (
    <>
      <Button
        icon={<img src={MagicSVG} alt='magic' width={20} style={{ color: '#fff' }} />}
        onClick={() => setProsPendingModalOpen(true)}
      >
        Prospecting
      </Button>
      {prospectingModalOpen && (
        <ProspectingModal
          open={prospectingModalOpen}
          onFinish={() => setProsPendingModalOpen(false)}
          onCancel={() => setProsPendingModalOpen(false)}
        />
      )}
    </>
  );
};

export default ProspectingAccounts;

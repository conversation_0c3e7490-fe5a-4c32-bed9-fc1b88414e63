import request from '@/utils/request.ts';
import type { ProspectingAccountProfile } from './data';

export interface SubmitDataType {
  account_id: string;
  user_query: string;
}

export const prospectingAccountsByAccountId = async (data: SubmitDataType) => {
  const currentUser = getCurrentUser();
  return await request('/api/sales-agent/prospecting-accounts', {
    method: 'post',
    data: {
      ...data,
      current_user: currentUser,
    },
  });
};

export const prospectingMoreAccountsByThreadId = async (thread_id: string) => {
  const currentUser = getCurrentUser();
  return await request('/api/sales-agent/prospecting-accounts/more', {
    method: 'post',
    data: {
      thread_id,
      current_user: currentUser,
    },
  });
};

export const getProspectingThreadByAccountId = async (accountId: string) => {
  return await request(`/api/sales-agent/prospecting-accounts/${accountId}/threads`, {
    method: 'get',
  });
};

export const getProspectingThreadProfileById = async (threadId: string) => {
  return await request(`/api/sales-agent/prospecting-accounts/thread/${threadId}`, {
    method: 'get',
  });
};

export interface AddAccountInfoType {
  user_id: string;
  account_info: ProspectingAccountProfile;
  request_headers: any;
}

export const addAccountToCRM = async (data: AddAccountInfoType) => {
  return await request('/api/sales-agent/add-account', {
    method: 'post',
    data,
  });
};

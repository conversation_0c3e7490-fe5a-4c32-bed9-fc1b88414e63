import React, { useCallback, useRef } from 'react';
import { MarkdownTypewriter } from 'react-markdown-typewriter';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';

interface MarkdownTypingPreview {
  markdownText: string;
}

const MarkdownTypingPreview: React.FC<MarkdownTypingPreview> = ({ markdownText }) => {
  const paragraphRef = useRef<HTMLDivElement>(null);

  const scrollToEnd = useCallback((ref: { current: HTMLSpanElement | null }) => {
    if (paragraphRef.current && ref.current) {
      let scrollTop = ref.current.offsetTop - paragraphRef.current.clientHeight / 2;
      paragraphRef.current.scrollTo({
        top: scrollTop,
        behavior: 'auto',
      });
    }
  }, []);

  return (
    <div
      ref={paragraphRef}
      style={{
        overflow: 'auto',
        height: '100%',
      }}
    >
      <MarkdownTypewriter
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        delay={20}
        motionProps={{
          characterVariants: {
            hidden: { opacity: 0 },
            visible: { opacity: 1, transition: { opacity: { duration: 0 } } },
          },
          onCharacterAnimationComplete: scrollToEnd,
        }}
        components={{
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          a({ node, ...props }) {
            return <a {...props} target='_blank' rel='noreferrer' />;
          },
        }}
      >
        {markdownText}
      </MarkdownTypewriter>
    </div>
  );
};

export default MarkdownTypingPreview;

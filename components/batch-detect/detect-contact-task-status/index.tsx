import type { MessageItemType, TaskStatusType } from '@/components/detect-contact-popover/data';
import { displayTextMap } from '@/components/detect-contacts/constants.ts';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Badge, Typography } from 'antd';
import React, { useMemo, useState } from 'react';

import DetectContactPopover from '@/components/detect-contact-popover';
import './index.css';

export const terminalStatusTypes = [
  'failed',
  'error',
  'finish',
  'completed',
  'success',
  'unknown',
  '500',
  'not_found',
  'canceled',
];

/**
 * 获取状态徽章颜色
 * @param type 状态类型
 * @returns 状态徽章颜色
 */

const badgeStatusType = [
  'success',
  'processing',
  'running',
  'error',
  'default',
  'warning',
  'finish',
  'thinking',
];

export const getStatusBadgeColor = (
  type: MessageItemType | TaskStatusType,
): 'success' | 'processing' | 'error' | 'default' | 'warning' | string => {
  switch (type) {
    case 'pending':
      return 'geekblue'; // 蓝色
    case 'running':
    case 'thinking':
      return 'processing';
    case 'completed':
    case 'success':
    case 'finish':
      return 'success';
    case 'failed':
    case 'error':
      return 'error';
    default:
      return 'default';
  }
};

interface DetectContactTaskStatusProps {
  accountId: string;
  taskId?: string;
  taskStatus?: TaskStatusType;
  accountName: string;
  style?: React.CSSProperties;
}

const DetectContactTaskStatus: React.FC<DetectContactTaskStatusProps> = ({
  accountId,
  taskId: propTaskId,
  taskStatus: propTaskStatus,
  accountName,
  style,
}) => {
  const [taskStatus, setTaskStatus] = useState<TaskStatusType | undefined>(propTaskStatus);
  const [taskId, setTaskId] = useState<string | undefined>(propTaskId);

  useEffect(() => {
    setTaskStatus(propTaskStatus);
    setTaskId(propTaskId);
  }, [propTaskId, propTaskStatus]);

  const isLoading = useMemo(
    () => (taskStatus ? !terminalStatusTypes.includes(taskStatus) : true),
    [taskStatus],
  );

  if (!taskId || !taskStatus) return null;

  const currentDisplayText = displayTextMap[taskStatus as keyof typeof displayTextMap] || '';

  return (
    <DetectContactPopover
      taskId={taskId}
      accountName={accountName}
      onTaskStatusUpdate={(status) => {
        setTaskStatus(status);
      }}
      accountId={accountId}
      placement='right'
    >
      {isLoading ? (
        <div
          style={{
            display: 'inline-block',
            minWidth: 40,
            minHeight: 20,
            lineHeight: '20px',
            cursor: 'pointer',
            zIndex: 99999,
          }}
        >
          <Badge
            style={{
              scale: 0.7,
            }}
            // @ts-ignore
            status={
              badgeStatusType.includes(taskStatus) ? getStatusBadgeColor(taskStatus) : undefined
            }
            // @ts-ignore
            color={
              !badgeStatusType.includes(taskStatus) ? getStatusBadgeColor(taskStatus) : undefined
            }
            text={
              <Typography.Text
                style={{
                  maxWidth: 120,
                  opacity: 0.68,
                  cursor: 'pointer',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  textAlign: 'right',
                  userSelect: 'none',
                }}
              >
                {currentDisplayText}
              </Typography.Text>
            }
          />
        </div>
      ) : (
        <div
          style={{
            display: 'flex',
            height: 20,
            lineHeight: 20,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <InfoCircleOutlined
            style={{
              fontSize: 10,
              cursor: 'pointer',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              display: 'block',
              textAlign: 'right',
              color: ['500', 'failed', 'error'].includes(taskStatus) ? '#faad14' : 'inherit',
              userSelect: 'none',
              ...style,
            }}
          />
        </div>
      )}
    </DetectContactPopover>
  );
};

export default DetectContactTaskStatus;

.ant-badge {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgb(0 0 0 / 85%);
  font-size: 14px;
  font-variant: tabular-nums;
  list-style: none;
  font-feature-settings: 'tnum';
  position: relative;
  display: inline-block;
  line-height: 1;
}

.ant-badge-status {
  line-height: inherit;
  vertical-align: baseline;
}

.ant-badge-status-dot {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 6px;
  height: 6px;
  vertical-align: middle;
  border-radius: 50%;
}

.ant-badge-color-geekblue {
  background-color: #2f54eb;
}

.ant-badge-status-success {
  background-color: #52c41a;
}

.ant-badge-status-processing {
  position: relative;
  background-color: #1890ff;
}

.ant-badge-status-processing::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid #1890ff;
  border-radius: 50%;
  animation: ant-status-processing 1.2s infinite ease-in-out;
  content: '';
}

.ant-badge-status-default {
  background-color: #d9d9d9;
}

.ant-badge-status-geekblue {
  background-color: #2f54eb;
}

.ant-badge-status-error {
  background-color: #ff4d4f;
}

.ant-badge-status-warning {
  background-color: #faad14;
}

.ant-badge-status-text {
  margin-left: 8px;
  color: rgb(0 0 0 / 85%);
  font-size: 14px;
}

.ant-badge-not-a-wrapper:not(.ant-badge-status) {
  vertical-align: middle;
}

@keyframes ant-status-processing {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

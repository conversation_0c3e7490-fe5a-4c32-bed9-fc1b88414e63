import { useEffect, useState } from 'react';
import ReactDOM from 'react-dom/client';

import mitt from 'mitt';

import { difference } from 'es-toolkit';

import BatchDetectButton from './batch-detect-button/index.tsx';

import type { ContentScriptContext } from '#imports';
import { createShadowRootUi } from '#imports';
import { waitElement } from '@1natsu/wait-element';

import { createAutoMountShadowRootUI } from '@/utils';
import { minimatch } from 'minimatch';
import type { TaskItem, TaskStatusType } from '../detect-contact-popover/data.d.ts';
import { fetchTaskResultByAccountIds } from '../detect-contacts/service.ts';
import DetectContactTaskStatus from './detect-contact-task-status/index.tsx';

// mitt event bus for task updates
type EventMap = {
  [key: `update-task-id-${string}`]: {
    taskId: string;
    taskStatus?: TaskStatusType;
    accountId: string;
  };
};

const emitter = mitt<EventMap>();

// 桥接组件，负责监听事件并驱动 taskId 变化
function DetectStatusEventBridge({
  accountId,
  accountName,
  ...props
}: {
  accountId: string;
  accountName: string;
  taskId?: string;
  taskStatus?: TaskStatusType;
}) {
  const [taskId, setTaskId] = useState<string | undefined>(props.taskId);
  const [taskStatus, setTaskStatus] = useState<TaskStatusType | undefined>(props.taskStatus);

  useEffect(() => {
    const handler = ({ taskId, taskStatus }: { taskId: string; taskStatus?: TaskStatusType }) => {
      setTaskId(taskId);
      if (taskStatus) {
        setTaskStatus(taskStatus);
      }
    };

    emitter.on(`update-task-id-${accountId}`, handler);
    return () => {
      emitter.off(`update-task-id-${accountId}`, handler);
    };
  }, [accountId]);

  return (
    <DetectContactTaskStatus
      taskId={taskId}
      taskStatus={taskStatus}
      accountId={accountId}
      accountName={accountName}
    />
  );
}

export const renderBatchDetect = async (
  ctx: ContentScriptContext,
  {
    accountIds,
  }: {
    accountIds: string[];
  },
) => {
  // id=moreActionsDiv
  await waitElement('#moreActionsDiv');

  await createAutoMountShadowRootUI<ReactDOM.Root | undefined>(ctx, {
    name: 'inhand-batch-detect-ui',
    position: 'inline',
    anchor: '#moreActionsDiv',
    pathMatches: ['/crm/*/tab/Accounts/custom-view/*/list'],
    append: 'last',
    inheritStyles: true,
    onMount: (container: HTMLElement, shadow: ShadowRoot, shadowHost: HTMLElement) => {
      // 设置host元素样式
      if (shadowHost) {
        shadowHost.classList.add('inhand-injected-element');
        shadowHost.setAttribute('data-inhand-injected', 'true');
        shadowHost.style.zIndex = '1000';
      }

      if (container) {
        let shadowHead = shadow.querySelector('head');
        if (!shadowHead) {
          shadowHead = document.createElement('head');
          shadow.appendChild(shadowHead);
        }
        // 添加事件隔离
        const eventShield = document.createElement('div');
        eventShield.className = 'event-shield';
        container.appendChild(eventShield);

        // 创建一个容器元素，所有渲染都在这个容器内
        const reactContainer = document.createElement('div');
        reactContainer.id = 'batch-button-container';

        const root = ReactDOM.createRoot(reactContainer);

        // 事件监听：接收 accountIds 更新（监听在 shadowHost 上）
        shadowHost.addEventListener('update-account-ids', (e: Event) => {
          const customEvent = e as CustomEvent<string[]>;
          root.render(
            <BatchDetectButton
              accountIds={customEvent.detail || []}
              onTaskIdReady={(accountId, taskId) => {
                emitter.emit(`update-task-id-${accountId}`, { taskId, accountId });
              }}
            />,
          );
        });

        root.render(<BatchDetectButton accountIds={accountIds || []} />);

        container.appendChild(reactContainer);
        return root;
      }

      return container;
    },
    onRemove: (root: ReactDOM.Root | undefined) => {
      root?.unmount();
    },
  });
};

const statusComponents = new Map<string, ShadowRootContentScriptUi<ReactDOM.Root | undefined>>();

const renderDetectStatus = async (
  ctx: ContentScriptContext,
  {
    accountId,
    task,
  }: {
    accountId: string;
    task?: TaskItem;
  },
): Promise<ShadowRootContentScriptUi<ReactDOM.Root | undefined>> => {
  const targetAnchorId = `#listView_${accountId}`;
  const shadowHostId = `inhand-accounts-detect-status-host-${accountId}`;

  const targetAnchorDom = await waitElement(targetAnchorId);

  const ui = await createShadowRootUi<ReactDOM.Root | undefined>(ctx, {
    name: shadowHostId,
    position: 'inline',
    anchor: `#listView_${accountId}`,
    append: 'after',
    inheritStyles: true,
    // isolateEvents: true,
    onMount: (container: HTMLElement, shadow: ShadowRoot, shadowHost: HTMLElement) => {
      if (shadowHost) {
        shadowHost.style.cssText = `
              display: inline-block;
              position: relative;
              width: max-content;
              min-width: 20px;
              float: right;
            `;
        shadowHost.classList.add('inhand-injected-element');
        shadowHost.setAttribute('data-inhand-injected', 'true');
        shadowHost.id = shadowHostId;
      }

      if (container) {
        const reactContainer = document.createElement('div');
        reactContainer.id = `inhand-accounts-detect-status-container-${accountId}`;

        const root = ReactDOM.createRoot(reactContainer);

        const accountName = targetAnchorDom?.textContent ?? '';

        root.render(
          <DetectStatusEventBridge
            accountId={accountId}
            accountName={accountName}
            taskId={task?._id}
            taskStatus={task?.status}
          />,
        );

        container.appendChild(reactContainer);
        return root;
      }

      return container;
    },
    onRemove: (root: ReactDOM.Root | undefined) => {
      root?.unmount();
      statusComponents.delete(accountId);
    },
  });

  ui.autoMount({
    once: true,
  });

  return ui;
};

let updateTasksInterval: number | undefined;

const handleInjectDetectStatusShadows: (
  ctx: ContentScriptContext,
  accountIds: string[],
) => Promise<void> = async (ctx: ContentScriptContext, accountIds: string[]) => {
  const tasksMap = new Map<string, any>();
  const updateTasks = async () => {
    const tasks = await fetchTaskResultByAccountIds(accountIds);
    if (!tasks.detail) {
      for (const task of tasks) {
        if (task.tags?.account_id) {
          tasksMap.set(task.tags.account_id, task);
          emitter.emit(`update-task-id-${task.tags.account_id}`, {
            taskId: task._id,
            accountId: task.tags.account_id,
            taskStatus: task.status,
          });
        }
      }
    }
  };
  window.clearInterval(updateTasksInterval);
  updateTasksInterval = ctx.setInterval(updateTasks, 15000);

  // observer location change, if not the account list page, stop updating tasks
  ctx.addEventListener(window, 'wxt:locationchange', (event) => {
    if (!minimatch(event.newUrl.pathname, '/crm/*/tab/Accounts/custom-view/*/list')) {
      window.clearInterval(updateTasksInterval);
    }
  });

  await updateTasks();

  // remove old accounts detection status ui
  statusComponents.forEach((ui) => ui.remove());
  statusComponents.clear();

  for (const accountId of accountIds) {
    const ui = await renderDetectStatus(ctx, {
      accountId,
      task: tasksMap.get(accountId),
    });
    statusComponents.set(accountId, ui);
  }
};

export const renderAccountsListPageUi = async (ctx: ContentScriptContext) => {
  const lyteExpTableRowGroup = await waitElement('.lyteExpTableRowGroup');
  const lyteExpTableWrapper = await waitElement('.lyteExpTableOrigTableInnerWrap', {});

  if (!lyteExpTableRowGroup || !lyteExpTableWrapper) {
    return;
  }

  let selectedAccountIds = new Set<string>();

  // 监控 lyteExpTableRowGroup 下， lyte-exptable-tr 的 class 变化
  let accountIds: string[] = [];

  async function detectElement() {
    const lyteExpTableTrs = lyteExpTableRowGroup.querySelectorAll('lyte-exptable-tr');

    const listViewRowSelectedTrs = Array.from(lyteExpTableTrs).filter((tr) =>
      tr.classList.contains('listViewRowSelected'),
    );
    // 找到 lyteExpTableTrs 的所有id
    const currentPageAccountIds = Array.from(lyteExpTableTrs).map(
      (tr) => tr.getAttribute('id') as string,
    );

    // if accountIds changed, update and inject
    if (accountIds.join(',') !== currentPageAccountIds.join(',')) {
      accountIds = currentPageAccountIds;
      handleInjectDetectStatusShadows(ctx, accountIds);
    }

    if (listViewRowSelectedTrs.length > 0) {
      // 获取 listViewRowSelectedTrs 的 id 属性
      const tempCheckedIds: string[] = listViewRowSelectedTrs.map((tr) => {
        return tr.getAttribute('id') as string;
      });

      const uncheckedIds = difference(currentPageAccountIds, tempCheckedIds);

      // 添加选中的ID
      tempCheckedIds.forEach((id) => selectedAccountIds.add(id));

      // 移除未选中的ID
      uncheckedIds.forEach((id) => selectedAccountIds.delete(id));
    }

    // 派发事件到 shadow host
    const shadowHost = document.querySelector('inhand-batch-detect-ui');
    if (shadowHost) {
      shadowHost.dispatchEvent(
        new CustomEvent('update-account-ids', {
          detail: Array.from(selectedAccountIds),
          bubbles: true,
          composed: true,
        }),
      );
    }
  }

  const obs = new MutationObserver(detectElement);
  obs.observe(lyteExpTableRowGroup, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class'],
  });

  detectElement();

  return () => {
    statusComponents.forEach((ui) => ui.remove());
    statusComponents.clear();

    obs.disconnect();
  };
};

export const renderAccountListPage = async (ctx: ContentScriptContext) => {
  createAutoMountShadowRootUI<any>(ctx, {
    name: 'inhand-account-list-page-ui',
    position: 'inline',
    anchor: 'crm-custom-outer-box[module_name="Accounts"]',
    pathMatches: ['/crm/*/tab/Accounts/custom-view/*/list'],
    append: 'last',
    onMount: (_, shadow) => {
      if (shadow) {
        shadow.querySelector('html')?.style.setProperty('display', 'none');
        shadow.querySelector('body')?.style.setProperty('display', 'none');
      }
      return renderAccountsListPageUi(ctx);
    },
  });
};

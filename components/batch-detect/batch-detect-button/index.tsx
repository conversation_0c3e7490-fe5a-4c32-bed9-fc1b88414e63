import React from 'react';

import { DownOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Dropdown, message, Space } from 'antd';

import type { CustomPromptsType } from '@/components/custom-prompt-modal';
import CustomPromptModalModal from '@/components/custom-prompt-modal';

import MagicIcon from '~/assets/magic.svg?react';
import { batchEnsureTaskIds } from './batch-server';
import './index.css';

interface BatchButtonProps {
  accountIds: string[];
  onTaskIdReady?: (accountId: string, taskId: string) => void;
}

const BatchDetectButton: React.FC<BatchButtonProps> = ({ accountIds, onTaskIdReady }) => {
  const [messageApi, contextHolder] = message.useMessage();

  const showErrorMessage = useCallback(
    (error: string) => {
      messageApi.error(error);
    },
    [messageApi],
  );

  const { run: handleBatchDetect } = useRequest(
    async (customPrompts?: CustomPromptsType) => {
      return await batchEnsureTaskIds({
        accountIds,
        customPrompts,
        onTaskIdReady,
        onError: (error: string) => {
          showErrorMessage(error);
        },
      });
    },
    {
      manual: true,
      refreshDeps: [accountIds],
    },
  );

  return (
    <>
      <Space id='detect-contacts-container' className='ant-space ant-space-align-center'>
        <Dropdown
          menu={{
            items: [
              {
                key: 'standard',
                onClick: () => handleBatchDetect(),
                label: 'Use Standard Detect',
              },
              {
                key: 'custom',
                label: <CustomPromptModalModal onConfirm={(prompt) => handleBatchDetect(prompt)} />,
              },
            ],
          }}
          overlayStyle={{
            width: 180,
          }}
        >
          <Button
            // @ts-ignore
            icon={<MagicIcon width={20} height={20} />}
            onClick={(e) => e.preventDefault()}
            className='ant-btn ant-btn-default batch-button'
          >
            <Space>
              Detect Contacts <DownOutlined />
            </Space>
          </Button>
        </Dropdown>
      </Space>
      {contextHolder}
    </>
  );
};

export default BatchDetectButton;

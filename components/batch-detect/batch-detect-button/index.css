/* Button 基础样式 */
.ant-btn {
  line-height: 1;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  border: 1px solid transparent;
  box-shadow: 0 2px #00000004;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  user-select: none;
  touch-action: manipulation;
  height: 32px;
  padding: 4px 15px;
  font-size: 14px;
  border-radius: 6px;
  color: #000000d9;
  border-color: #d9d9d9;
  background-color: #fff;
}

.ant-btn:hover,
.ant-btn:focus {
  color: #40a9ff;
  background: #fff;
  border-color: #40a9ff;
  text-decoration: none;
  outline: 0;
  box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
}

.ant-btn:active {
  color: #096dd9;
  background: #fff;
  border-color: #096dd9;
  outline: 0;
  box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
}

.ant-btn[disabled],
.ant-btn[disabled]:hover,
.ant-btn[disabled]:focus,
.ant-btn[disabled]:active {
  color: rgb(0 0 0 / 25%);
  background: #f5f5f5;
  border-color: #d9d9d9;
  text-shadow: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Space 组件样式 */
.ant-space {
  display: inline-flex;
  align-items: center;
}

.ant-space-align-center {
  align-items: center;
}

/* batch-button 专用样式 */
.batch-button {
  background: linear-gradient(88deg, #6697c9 -20.28%, #da67cf 119.17%);
  color: #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.batch-button:hover,
.batch-button:focus,
.batch-button:active {
  background: linear-gradient(88deg, #a8c3eb -20.28%, #eaa9eb 119.17%);
  color: #fff;
  border-color: transparent;
}

.batch-button .ant-space {
  align-items: center;
  line-height: 1;
  gap: 4px;
}

.batch-button * {
  line-height: 1;
}

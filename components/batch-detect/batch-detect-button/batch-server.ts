import type { CustomPromptsType } from '@/components/custom-prompt-modal';
import type { CompanyInfoType } from '@/components/detect-contacts/service';
import { startDetectTask } from '@/components/detect-contacts/service';

/**
 * 批量处理参数类型
 */
export interface BatchEnsureTaskIdsParams {
  accountIds: string[];
  customPrompts?: CustomPromptsType;
  onTaskIdReady?: (accountId: string, taskId: string) => void;
  onError?: (error: string) => void;
}

/**
 * 批量处理账户
 * @param params 批量处理参数
 * @returns 批量处理结果数组Promise
 */
export const batchEnsureTaskIds = async ({
  accountIds,
  customPrompts,
  onTaskIdReady,
  onError,
}: BatchEnsureTaskIdsParams): Promise<
  {
    accountId: string;
    taskId: string;
  }[]
> => {
  if (!accountIds || accountIds.length === 0) {
    onError?.('No account IDs provided');
    return [];
  }

  const ownerId = (await getUserId()) as string;

  const promises = accountIds.filter(Boolean).map(async (accountId) => {
    try {
      const companyInfo: CompanyInfoType = {
        accountId,
        ownerId,
        customPrompts,
      };
      // 无论是创建成功，还是已经在运行的任务都会返回 task_id
      const { task_id: taskId } = await startDetectTask(companyInfo);

      if (taskId) {
        onTaskIdReady?.(accountId, taskId);
        return { accountId, taskId };
      }
      return undefined;
    } catch (error: any) {
      onError?.(`Error processing account ${accountId}: ${JSON.stringify(error)}`);
      return undefined;
    }
  });

  const results = await Promise.all(promises);

  return results.filter(Boolean) as {
    accountId: string;
    taskId: string;
  }[];
};

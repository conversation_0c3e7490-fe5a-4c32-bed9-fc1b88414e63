import { Typography } from 'antd';
import markdownit from 'markdown-it';
import markdownitExternalLink from 'markdown-it-external-link';
import type { ReactNode } from 'react';
import React from 'react';

interface MarkdownViewProps {
  content: string;
  className?: string;
}

const md = markdownit({
  html: true,
  breaks: true,
  linkify: true,
});

md.use(markdownitExternalLink);

const MarkdownView: React.FC<MarkdownViewProps> = ({ content, className }): ReactNode => (
  <Typography className={className}>
    {/* biome-ignore lint/security/noDangerouslySetInnerHtml: used in demo */}
    <div dangerouslySetInnerHTML={{ __html: md.render(content) }} />
  </Typography>
);

export default MarkdownView;

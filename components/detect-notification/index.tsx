import { Button, notification } from 'antd';
import { forwardRef, useCallback, useImperativeHandle } from 'react';

export interface DetectNotificationProps {
  total: number;
  accountName: string;
  taskId?: string;
}

export interface DetectNotificationRef {
  openNotification?: () => void;
}

const DetectNotification = forwardRef<DetectNotificationRef, DetectNotificationProps>(
  ({ total, accountName, taskId }, ref) => {
    const [api, contextHolder] = notification.useNotification({
      top: 64,
    });

    const reloadPage = useCallback(() => {
      try {
        // 先关闭通知，避免阻塞页面
        api.destroy(taskId);
        // 尝试在正确的上下文中执行 reload
        if (window.top && window.top !== window) {
          window.top.location.reload();
        } else {
          window.location.reload();
        }
      } catch (error) {
        console.error('Failed to reload page:', error);
        // 降级方案：直接在当前窗口 reload
        window.location.reload();
      }
    }, [api, taskId]);

    const openNotification = () => {
      api.open({
        message: 'Contact Discovery Completed',
        description: (
          <>
            The contact discovery task for company <b>{accountName}</b> is finished, and{' '}
            {total || 0} new contact(s) have been added. <br />
            You can click the reload button to refresh the page and view the latest information.
          </>
        ),
        duration: 8,
        style: { width: 400 },
        actions: [
          <Button
            key='reload'
            type='primary'
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              reloadPage();
            }}
          >
            Reload
          </Button>,
        ],
        key: taskId,
        onClose: () => {
          api.destroy(taskId);
        },
      });
    };

    useImperativeHandle(ref, () => ({
      openNotification,
    }));

    return contextHolder;
  },
);

export default DetectNotification;

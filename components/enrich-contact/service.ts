import request from '@/utils/request.ts';

export const fetchEnrichStatus = async (contactId: string) => {
  return await request(`/api/sales-agent/contact/${contactId}/enrich/status`, {
    method: 'get',
  });
};

export const enrichContact = async (contactId: string) => {
  return await request(`/api/sales-agent/contact/${contactId}/enrich`, {
    method: 'get',
  });
};

export const fetchContactEnrichResultByRequestId = async (requestId: string) => {
  return await request(`/api/sales-agent-webhook/apollo/${requestId}`, {
    method: 'get',
  });
};

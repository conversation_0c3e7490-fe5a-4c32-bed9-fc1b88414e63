import React from 'react';

import { CloseCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useDeepCompareEffect, useRequest } from 'ahooks';
import { Button, Descriptions, Popover, Space } from 'antd';

import { enrichContact, fetchEnrichStatus } from './service.ts';

import { getContactId } from '@/utils';
import MagicSVG from '~/assets/magic-colorful.svg';

type StatusStatusType = 'not_found' | 'pending' | 'success' | 'failed';

const enrichStatusMap: Record<StatusStatusType, string> = {
  not_found: 'Not Found',
  pending: 'Pending',
  success: 'Success',
  failed: 'Failed',
};

interface StatueDateItem {
  status: StatusStatusType;
  updated_at?: string;
  request_id?: string;
}

const EnrichContact: React.FC = () => {
  const contactId = getContactId();
  const [loading, setLoading] = useState<boolean>(true);
  const [statusData, setStatusData] = useState<StatueDateItem>();
  const [error, setError] = useState<Record<string, any>>();

  const {
    data: poolingData,
    cancel: cancelPoolingQueryStatus,
    run: handleQueryRealtimeStatus,
  } = useRequest(
    async () => {
      if (contactId) {
        const data = await fetchEnrichStatus(contactId);
        setStatusData(data);
        if (data?.detail) {
          setError(data.detail);
          setLoading(false);
        }
        return data;
      }
    },
    {
      manual: true,
      pollingInterval: 10000,
      refreshDeps: [contactId],
    },
  );

  // query last enrich status
  useRequest(
    async () => {
      setLoading(true);
      if (contactId) {
        const data = await fetchEnrichStatus(contactId);
        if (data?.status === 'pending') {
          setLoading(true);
          handleQueryRealtimeStatus();
        } else if (data?.detail) {
          setError(data.detail);
          setLoading(false);
        } else {
          setStatusData(data);
          setLoading(false);
        }
      }
      return undefined;
    },
    {
      refreshDeps: [contactId],
    },
  );

  // start new enrich
  const { run: enrichRun } = useRequest(
    async () => {
      setLoading(true);
      setError(undefined);
      if (contactId) {
        const { request_id, detail } = await enrichContact(contactId);
        if (request_id) {
          handleQueryRealtimeStatus();
        } else if (detail) {
          setError(detail);
          setLoading(false);
        } else {
          setLoading(false);
        }
      }
    },
    {
      refreshDeps: [contactId],
      manual: true,
    },
  );

  useDeepCompareEffect(() => {
    if (poolingData?.status !== 'pending') {
      cancelPoolingQueryStatus();
      setLoading(false);
      if (poolingData?.status === 'success') {
        window.location.reload();
      }
    }
  }, [poolingData]);

  return (
    <Space direction='horizontal' size={8}>
      <Button
        loading={loading}
        style={{
          alignItems: 'center',
          gap: '8px',
          verticalAlign: 'middle',
        }}
        onClick={() => enrichRun()}
        icon={<img src={MagicSVG} alt='magic' width={20} style={{ color: '#fff' }} />}
      >
        Enrich Phone Number
      </Button>
      {statusData && statusData?.updated_at && !loading && (
        <Popover
          title={
            <Descriptions
              title='Last Enrich Status'
              items={[
                {
                  label: 'Status',
                  children: enrichStatusMap[statusData.status ?? 'not_found'],
                },
                {
                  label: 'Updated At',
                  children: statusData.updated_at
                    ? new Date(statusData.updated_at).toLocaleDateString()
                    : '-',
                },
              ]}
              style={{
                width: 300,
              }}
              column={1}
            />
          }
          placement='top'
          styles={{
            root: {
              position: 'fixed',
              zIndex: 99999,
            },
            body: {
              backgroundColor: '#fff',
              boxShadow: '0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08)',
              borderRadius: '4px',
              padding: '12px',
            },
          }}
        >
          <InfoCircleOutlined
            style={{
              // fontSize: 10,
              cursor: 'pointer',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              display: 'block',
              textAlign: 'right',
              color: statusData?.status === 'failed' ? '#faad14' : undefined,
            }}
          />
        </Popover>
      )}
      {error && (
        <Popover
          title={error?.messages}
          getPopupContainer={(node) => node.parentElement!}
          placement='top'
          styles={{
            root: {
              position: 'fixed',
              zIndex: 99999,
            },
            body: {
              backgroundColor: '#fff',
              boxShadow: '0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08)',
              borderRadius: '4px',
              padding: '12px',
            },
          }}
        >
          <CloseCircleOutlined
            style={{
              // fontSize: 10,
              cursor: 'pointer',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              display: 'block',
              textAlign: 'right',
              color: 'red',
            }}
          />
        </Popover>
      )}
    </Space>
  );
};

export default EnrichContact;

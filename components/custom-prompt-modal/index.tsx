import { Form, Input, Modal, Typography } from 'antd';
import React, { useState } from 'react';

export interface CustomPromptsType {
  prompt: string;
  coverSystemPrompt: boolean;
}

type CustomPromptModalProps = {
  onConfirm: (data: CustomPromptsType) => void;
};

const CustomPromptModal: React.FC<CustomPromptModalProps> = ({ onConfirm }) => {
  const [form] = Form.useForm();
  const [open, setOpen] = useState<boolean>(false);
  return (
    <>
      <Typography.Text onClick={() => setOpen(true)}>Use Custom AI Prompt</Typography.Text>
      <Modal
        title='Custom AI Prompt'
        open={open}
        onCancel={() => setOpen(false)}
        okButtonProps={{
          htmlType: 'submit',
          onClick: async () => {
            const values = await form.validateFields();
            onConfirm?.(values);
            setOpen(false);
          },
        }}
        destroyOnHidden
      >
        <Form
          form={form}
          initialValues={{
            coverSystemPrompt: false,
          }}
          layout='vertical'
          onFinish={(values) => onConfirm?.(values)}
          clearOnDestroy
        >
          <Form.Item
            label='Your Custom Filter Strategies'
            name='prompt'
            required
            rules={[{ required: true, message: 'Please input your filter strategy' }]}
          >
            <Input.TextArea
              placeholder='Please input your filter strategies, eg: Department, Business background, Title, Experience etc. '
              rows={8}
            />
          </Form.Item>{' '}
        </Form>
      </Modal>
    </>
  );
};

export default CustomPromptModal;

import type { DetectNotificationRef } from '@/components/detect-notification';
import DetectNotification from '@/components/detect-notification';
import { useRequest, useUnmount } from 'ahooks';
import type { PopoverProps } from 'antd';
import { message as antdMessage, Popover } from 'antd';
import React, { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { StreamMessage, TaskStatusType } from './data';
import { fetchTaskState } from './service';

import { fetchEventSource } from '@microsoft/fetch-event-source';

import 'antd/dist/reset.css';
import TaskStatusTimeline from './task-status-timeline';

export const terminalStatusTypes = [
  'failed',
  'error',
  'finish',
  'completed',
  'success',
  'unknown',
  '500',
  'not_found',
  'canceled',
];

interface DetectContactPopoverProps extends PopoverProps {
  accountId: string;
  accountName: string;
  taskId?: string;
  children?: React.ReactNode;
  onTaskStatusUpdate?: (status: TaskStatusType, error?: string) => void;
}

const DetectContactPopover: React.FC<DetectContactPopoverProps> = forwardRef<
  HTMLDivElement,
  DetectContactPopoverProps
>(
  (
    { accountId, accountName, taskId, onTaskStatusUpdate, children, placement, ...restProps },
    ref,
  ) => {
    const detectNotificationRef = useRef<DetectNotificationRef>(null);
    const ctrlRef = useRef<AbortController | null>(null);
    const [messages, setMessages] = useState<StreamMessage[]>([]);
    const [error, setError] = useState<string | undefined>();
    const [total, setTotal] = useState<number>(0);
    const [taskStatus, setTaskStatus] = useState<TaskStatusType | undefined>(undefined);

    const [messageApi, contextHolder] = antdMessage.useMessage();

    const notificationTimerRef = useRef<NodeJS.Timeout | null>(null);

    const isLoading = useMemo(
      () => (taskStatus ? !terminalStatusTypes.includes(taskStatus) : true),
      [taskStatus],
    );

    const MAX_NOTIFICATION_RETRY = 5;
    const NOTIFICATION_RETRY_DELAY = 100;

    const handleNotification = useCallback(() => {
      if (notificationTimerRef.current) {
        clearTimeout(notificationTimerRef.current);
        notificationTimerRef.current = null;
      }

      let retryCount = 0;

      const tryOpenNotification = () => {
        if (detectNotificationRef.current?.openNotification) {
          detectNotificationRef.current.openNotification();
        } else if (retryCount < MAX_NOTIFICATION_RETRY) {
          retryCount += 1;
          setTimeout(tryOpenNotification, NOTIFICATION_RETRY_DELAY);
        } else {
          // 可选：记录日志
          // console.warn('DetectNotification ref not ready after retries');
        }
      };

      notificationTimerRef.current = setTimeout(() => {
        tryOpenNotification();
      }, 2000);
    }, []);

    // 组件卸载时清理定时器
    useEffect(() => {
      return () => {
        if (notificationTimerRef.current) {
          clearTimeout(notificationTimerRef.current);
        }
      };
    }, []);

    const showErrorMessage = useCallback(
      (error: string) => {
        messageApi.error({ content: error, key: taskId });
      },
      [messageApi, taskId],
    );

    const { run: handleConnectSSE, cancel: cancelConnectSSE } = useRequest(
      async (taskId) => {
        try {
          if (ctrlRef.current) {
            ctrlRef.current.abort();
          }
          setMessages([]);

          const ctrl = new AbortController();
          ctrlRef.current = ctrl;

          const streamURL = `${BASE_URL}/api/sales-agent/tasks/${taskId}/stream`;

          try {
            await fetchEventSource(streamURL, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'x-api-key': X_API_KEY,
              },
              signal: ctrl.signal,
              onopen: async (event) => {
                console.info('[inhand] SSE connection opened', event);
                setTaskStatus('pending');
              },
              onmessage: async (event) => {
                const data: StreamMessage = JSON.parse(event.data);
                setMessages((prev) => {
                  return [...prev, data];
                });

                const newStatus: TaskStatusType = data.error
                  ? 'failed'
                  : data.type === 'finish'
                    ? 'completed'
                    : 'running';

                setTaskStatus(newStatus);
                onTaskStatusUpdate?.(newStatus, data.error);
                if (data.error) {
                  setError(data.error);
                }
                if (data.result?.total !== undefined) {
                  setTotal(data.result.total);
                }

                if (
                  ['completed', 'success', 'finish'].includes(newStatus) &&
                  (data.result?.total ?? 0) > 0
                ) {
                  handleNotification();
                }
              },
              onerror: (event) => {
                console.error('[inhand] SSE connection error', event);
                showErrorMessage('SSE connection error');
              },
              onclose: async () => {
                console.info('[inhand] SSE connection closed');
              },
            });
          } catch {
            console.error('Failed to connect SSE');
            showErrorMessage('Failed to connect SSE');
          }
        } catch (error) {
          console.error(error);
          showErrorMessage('Failed to connect SSE');
        }
      },
      {
        manual: true,
        refreshDeps: [accountId, taskId],
      },
    );
    const { run: handleFetchTaskState } = useRequest(
      async () => {
        if (!taskId) return;
        // 根据 taskid 获取到 task 状态
        const res = await fetchTaskState(taskId);
        if (res) {
          const { status, error: resError, result, messages: resMessages } = res;
          // 判断状态是否为 terminalStatusTypes 中，表示任务已经完成。
          setTaskStatus(status);
          onTaskStatusUpdate?.(res.status, res.error);
          if (terminalStatusTypes.includes(status)) {
            setError(resError);
            setTotal(result?.total ?? result?.data?.length ?? 0);
            if (resMessages) {
              setMessages(resMessages);
            }
          } else {
            handleConnectSSE(taskId);
          }
        }

        return res;
      },
      {
        manual: true,
        refreshDeps: [taskId],
      },
    );

    // 如果 taskId 变化，则检测任务状态
    useEffect(() => {
      if (taskId) {
        handleFetchTaskState();
      }
    }, [taskId]);

    useUnmount(() => {
      cancelConnectSSE();
    });

    const renderTimeLine = useMemo(
      () => <TaskStatusTimeline messages={messages} loading={isLoading} error={error} />,
      [messages, isLoading, error],
    );

    return (
      // @ts-ignore
      <div {...restProps} ref={ref}>
        <Popover
          title={
            taskStatus && (
              <div
                style={{
                  userSelect: 'none',
                  pointerEvents: 'auto',
                  // padding: 12,
                }}
              >
                {renderTimeLine}
              </div>
            )
          }
          placement={placement}
        >
          {children}
        </Popover>
        <DetectNotification
          ref={detectNotificationRef}
          total={total}
          accountName={accountName}
          taskId={taskId}
        />
        {contextHolder}
      </div>
    );
  },
);

export default DetectContactPopover;

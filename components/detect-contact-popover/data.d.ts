// task status type
export type TaskStatusType =
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'canceled'
  | 'success'
  | 'not_found';

// message item type
export type MessageItemType =
  | 'thinking'
  | 'pending'
  | 'text'
  | 'data'
  | 'tool_call'
  | 'tool_result'
  | 'step_start'
  | 'step_end'
  | 'finish'
  | 'error'
  | 'not_found';
export interface StreamMessage {
  type: MessageItemType;
  content?: string;
  data?: Record<string, any>;
  tool_call_id?: string;
  tool_name?: string;
  tool_input?: Record<string, any>;
  tool_result?: any;
  error?: string;
  result?: any;
  timestamp: string; // ISO datetime string
}

export interface TaskEvent {
  type: string;
  name: string;
  ts: string; // ISO datetime string
  details?: Record<string, any>;
}

export interface TaskResult {
  status: TaskStatus;
  completed_at: string; // ISO datetime string
  // Present on successful task completion
  data?: any[];
  total?: number;
  content?: string;
  value?: any;
  // Present on task failure
  error?: string;
}

/**
 * Represents the main task data structure stored in MongoDB and returned by the API endpoints.
 * This is the response type for both `/api/sales-agent/account/{accountId}/task` and `/api/sales-agent/tasks/{taskId}`.
 */
export interface TaskItem {
  _id?: string; // Task ID
  status?: TaskStatus;
  type?: TaskType;
  created_at?: string; // ISO datetime string
  updated_at?: string; // ISO datetime string
  started_at?: string; // ISO datetime string
  completed_at?: string; // ISO datetime string
  tags?: {
    account_id: string;
    task_type: string;
    [key: string]: unknown;
  };
  messages?: StreamMessage[];
  events?: TaskEvent[];
  error?: string;
  result?: TaskResult;
}

import MarkdownView from '@/components/markdown-view';
import { Timeline } from 'antd';
import { delay } from 'es-toolkit';
import React, { useEffect, useMemo, useRef } from 'react';
import type { MessageItemType, StreamMessage, TaskStatusType } from '../data';

export const getTimelineItemColor = (type: MessageItemType | TaskStatusType) => {
  switch (type) {
    case 'pending':
    case 'running':
      return 'geekblue'; // 蓝色
    case 'thinking':
      return 'blue';
    case 'completed':
    case 'success':
    case 'finish':
      return 'green';
    case 'failed':
    case 'error':
      return 'red';
    default:
      return 'gray';
  }
};

interface TaskStatusTimelineProps {
  messages: StreamMessage[];
  loading?: boolean;
  error?: string;
}

const TaskStatusTimeline: React.FC<TaskStatusTimelineProps> = ({ messages, loading, error }) => {
  const timelineRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    delay(200).then(() => {
      if (timelineRef.current) {
        timelineRef.current.scrollTo({
          top: timelineRef.current.scrollHeight,
          behavior: 'smooth',
        });
      }
    });
  }, [messages?.length]);

  const timelineItems = useMemo(() => {
    if (messages?.length && messages.length > 0) {
      return messages.map((v: StreamMessage) => ({
        children: (
          <MarkdownView
            content={
              v.type === 'finish'
                ? (v?.content ?? v?.error ?? error ?? 'Task completed')
                : (v.content ?? '')
            }
          />
        ),
        color: getTimelineItemColor(
          (v?.error ? 'error' : v.type) as MessageItemType | TaskStatusType,
        ),
        style: {
          padding: '0',
        },
      }));
    }
    return [];
  }, [messages, error]);

  return (
    <Timeline
      items={timelineItems}
      // @ts-ignore
      ref={timelineRef}
      pending={loading ? (timelineItems?.length > 0 ? 'Thinking' : 'Pending') : undefined}
      style={{
        width: 320,
        maxHeight: 480,
        overflowY: 'auto',
        padding: '12px 8px',
      }}
    />
  );
};

export default TaskStatusTimeline;
